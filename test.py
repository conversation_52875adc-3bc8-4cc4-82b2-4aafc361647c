
def simulate_blackjack(n_simulations=100000):
    """模拟n局21点，统计双方同时爆牌的概率"""
    both_bust = 0
    dealer_bust = 0
    for _ in range(n_simulations):
        deck = create_deck()
        random.shuffle(deck)
        # 发初始牌（玩家2张，庄家2张，庄家1张明牌）
        dealer_hand = [deck.pop(), deck.pop()]
        player_hand = [deck.pop(), deck.pop()]
        
        # 玩家回合
        player_bust = player_turn_strategy(dealer_hand[0], deck.copy(), player_hand.copy())
        
        # 庄家回合（仅当玩家未爆牌时进行）
        dealer_bust = False
        if not player_bust:
            dealer_bust = dealer_turn(deck.copy(), dealer_hand.copy())
       
        # 统计双方爆牌
        if player_bust and dealer_bust:
            both_bust += 1
        # if dealer_bust and not player_bust:
        #     dealer_bust += 1
        #print(f"玩家手牌: {player_hand}, 庄家手牌: {dealer_hand}, 玩家爆牌: {player_bust}, 庄家爆牌: {dealer_bust}, 轮次: {_ + 1}, 牌数: {len(deck)}")
    
    probability = both_bust / n_simulations
    print(f"模拟次数: {n_simulations}")
    print(f"双方同时的概率: {probability:.2%}")
    