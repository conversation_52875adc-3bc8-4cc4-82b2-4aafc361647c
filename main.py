import random
from player_strategy import should_hit_hard, should_hit_soft, should_double_soft, should_double_hard

def create_deck():
    """创建一副扑克牌（52张，A=11，J/Q/K=10）"""
    ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
    deck = ranks * 8  # 4种花色
    return deck

def calculate_hand_value(hand):
    """计算手牌的点数（A可作1或11）"""
    value = 0
    aces = 0
    soft = False  # 是否为软牌（含A）
    for card in hand:
        if card in ['J', 'Q', 'K']:
            value += 10
        elif card == 'A':
            value += 11
            aces += 1
            soft = True
        else:
            value += int(card)

    # 处理A的软硬牌
    while value > 21 and aces > 0:
        value -= 10  # A从11变为1
        aces -= 1
        if aces == 0:
            soft = False
    return soft, value

def simple_player_turn(deck, hand):
    """玩家回合（按基本策略要牌或停牌）"""
    while True:
        value = calculate_hand_value(hand)
        if value >= 17:  # 基本策略：17点以上停牌
            break
        hand.append(deck.pop())  # 要牌
    return value > 21  # 是否爆牌

def player_turn_strategy(dealer_card, deck, hand):
    """玩家回合（模仿庄家策略：16点以下要牌，17点以上停牌）"""
    while True:
        soft, value = calculate_hand_value(hand)
        if soft:
            if should_hit_soft(value, dealer_card):
                hand.append(deck.pop())
            else:
                break
        else:
            if should_hit_hard(value, dealer_card, hand):
                hand.append(deck.pop())
            else:
                break
    soft, value = calculate_hand_value(hand)
    return value > 21, value, hand

def dealer_turn(deck, hand):
    """庄家回合（固定规则：16点以下要牌，17点以上停牌）"""
    while True:
        soft, value = calculate_hand_value(hand)
        if value < 17:
            hand.append(deck.pop())
        else:
            break
    soft, value = calculate_hand_value(hand)
    return value > 21, value, hand

def is_natural(hand):
    """严格判断是否天成（A+10/J/Q/K，顺序无关）"""
    return ('A' in hand) and any(card in ['10','J','Q','K'] for card in hand)

def simulate_round(deck):
    """模拟一局游戏"""
    dealer_hand = [deck.pop(), deck.pop()]
    player_hand = [deck.pop(), deck.pop()]
    multiplier = 1  # 初始倍数

    # 加倍
    is_soft, player_value = calculate_hand_value(player_hand.copy())
    if is_soft:
        should_double = should_double_soft(player_value, dealer_hand[0], player_hand)
    else:
        should_double = should_double_hard(player_value, dealer_hand[0], player_hand)
    # 策略
    player_bust, player_value, player_hand = player_turn_strategy(dealer_hand[0], deck.copy(), player_hand.copy())
    
    # 庄家回合（仅玩家未爆牌时进行）
    dealer_value, dealer_bust = None, False
    if not player_bust:
        dealer_bust, dealer_value, dealer_hand = dealer_turn(deck.copy(), dealer_hand.copy())
        _, dealer_value = calculate_hand_value(dealer_hand.copy())

    if should_double:
        multiplier = 2
    # 判定胜负
    if player_bust:
        return -1 * multiplier  # 玩家爆牌
    elif dealer_bust:
        return 1 * multiplier  # 庄家爆牌
    elif player_value > dealer_value:
        return 1 * multiplier  # 玩家胜
    elif player_value < dealer_value:
        return -1 * multiplier  # 庄家胜
    elif is_natural(player_hand) and not is_natural(dealer_hand):
        return 1.5 * multiplier # 玩家天成胜
    else:
        return 0  # 平局

def simulate_10k_rounds(round):
    """模拟10,000轮"""
    player_balance = 0
    for _ in range(round):
        deck = create_deck()
        random.shuffle(deck)
        result = simulate_round(deck)
        player_balance += result
    
    house_edge = -player_balance / round
    print(f"玩家最终盈亏: {player_balance}元，总轮次: {round}")
    print(f"庄家优势: {house_edge:.2%}")

simulate_10k_rounds(100000)
