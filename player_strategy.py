def should_hit_hard(player_total: int, dealer_card: str, player_cards) -> bool:
    dealer_value = card_value(dealer_card)
    num_cards = len(player_cards)  # 玩家手牌数量
    if dealer_value <= 3:
        return player_total < 13
    elif dealer_value <= 6:
        return player_total < 12
    else:
        if player_total == 16:
            return num_cards == 2  
        if dealer_card in ['10', 'J', 'Q', 'K'] and player_cards == ['7','7']:
            return player_total < 17
        return player_total < 17
    
def card_value(card: str) -> int:
    """返回牌的点数值"""
    if card in ['J', 'Q', 'K']:
        return 10
    elif card == 'A':
        return 11  # A初始视为11
    else:
        return int(card)

def should_hit_soft(player_total: int, dealer_card: str) -> bool:
    dealer_value = card_value(dealer_card)
    if player_total >= 19:
        return False  # Soft 20 or higher: always stand
    if player_total == 18:
        return dealer_value in [9, 10]  # only hit if dealer is strong
    return True  # Soft 17 or less: always hit

def should_double_soft(player_total, dealer_card, player_cards) -> bool:
    """判断是否可以双倍下注"""
    if dealer_card in ['3', '4', '5', '6'] and player_total == 18 and len(player_cards) == 2:
        return True
    elif dealer_card in ['2', '3', '4', '5', '6'] and player_total == 17 and len(player_cards) == 2:
        return True
    elif dealer_card in ['4', '5', '6'] and player_total == 16:
        return True
    elif dealer_card in ['4', '5', '6'] and player_total == 15 and len(player_cards) == 2:
        return True
    elif dealer_card in ['4','5', '6'] and player_total == 14 and len(player_cards) == 2:
        return True
    elif dealer_card in ['4', '5', '6'] and player_total == 13 and len(player_cards) == 2:
        return True
    elif dealer_card in ['5', '6'] and player_cards == ['A', 'A']:
        return True
    return False

def should_double_hard(player_total, dealer_card, player_cards) -> bool:
    """判断是否可以双倍下注"""
    if player_total == 11:
        return True
    elif player_total == 10 and dealer_card in ['2', '3', '4', '5', '6', '7', '8', '9']:
        return True
    elif dealer_card in ['2', '3', '4', '5', '6'] and player_total == 9:
        return True
    elif player_total == 8 and dealer_card in ['5', '6'] and len(player_cards) == 2 and player_cards != ['A', 'A']:
        return True
    return False

